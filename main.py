
from env import ENVIRONMENT
from cskb.cskb_operation import CSKBOperation
from database.mysql import MySQLDatabase
from rag.chunking import load_and_chunk_base, optimized_upload_strategy
from rag.embedding import batch_upload_knowledge
from rag.chunking_new import load_and_chunk_base_optimized, upload_chunk

def get_cskb_agents_info() -> list:
    """获取CSKB知识库的agent_id和access_token"""
    db_name = 'svwcopilot'
    db = MySQLDatabase(db_name)
    # sql = f'SELECT * FROM cskb_agent_key where env = \'{ENVIRONMENT}\''
    sql = f'SELECT * FROM cskb_agent_key'
    results = db.execute_query(sql)
    return results


def init_all_cskb_to_milvus():
    cskb_agents_info = get_cskb_agents_info()
    for agent in cskb_agents_info:
        if agent['is_valid'] == 1:
            cskb_operation = CSKBOperation(db=db)
            knowledge_list = cskb_operation.get_agent_all_knowledge(agent)
            load_and_chunk_base(agent, knowledge_list)




if __name__ == '__main__':
    # import asyncio
    db = 'cskb'
    cskb_operation = CSKBOperation(db=db)
    # agents = get_cskb_agents_info()
    # agents = [{'id': 8, 'agent_id': 'f69b984010fd9c6d03e1e7387938af0e', 'agent_name': '研发知识库', 'agent_name_en': 'RD', 'access_token': '3dde0816-3828-4447-9dfd-4ccf4fe9a243', 'env': 'prod', 'is_valid': 1}, {'id': 10, 'agent_id': '5149f01c1b1eeb9b43c608e576c932a4', 'agent_name': '制造知识库', 'agent_name_en': 'PF', 'access_token': '258d40e2-e4bb-412a-bb24-b12939d2b929', 'env': 'prod', 'is_valid': 1}, {'id': 11, 'agent_id': '3c3d93a086a0cf95e2ef7bf9fbd0e473', 'agent_name': '审计知识库', 'agent_name_en': 'MI', 'access_token': 'd3195270-e7b7-4c80-802c-fde89d858740', 'env': 'prod', 'is_valid': 1}, {'id': 12, 'agent_id': '09cea711e4503c6c64957e67393fa1ba', 'agent_name': '规划知识库', 'agent_name_en': 'PM', 'access_token': 'dffc4ac8-89cc-4d6e-817c-da14ab51f79f', 'env': 'prod', 'is_valid': 1}, {'id': 13, 'agent_id': 'ddc888f458676a2fb97488ddcf44635e', 'agent_name': '物流知识库', 'agent_name_en': 'PL', 'access_token': '48a7537a-51c4-48d3-bb7b-11505dae3869', 'env': 'prod', 'is_valid': 1}, {'id': 14, 'agent_id': '321df75bcbbc6502ef68a70d687880cf', 'agent_name': '质保知识库', 'agent_name_en': 'MQ', 'access_token': 'd2a54aec-95db-4c90-819e-8b8adc802eed', 'env': 'prod', 'is_valid': 1}]
    agents = [{'id': 1, 'agent_id': 'ba96968f61b6b72a6ef97c87ab99bee6', 'agent_name': '零号员工知识库', 'agent_name_en': 'EmpZero11111', 'access_token': '99f747e3-687b-4a5f-81c3-51513ea192a5', 'env': 'prod', 'is_valid': 1}]
    for agent in agents:
        results = cskb_operation.get_agent_all_knowledge(agent)
        chunk_list = load_and_chunk_base_optimized(results)
        import pandas as pd
        a = pd.DataFrame(chunk_list)
        a.to_csv('chunk_list.csv', index=False)
        # upload_chunk(agent, chunk_list)
    # print(len(agents))
    # print('agents', agents)
    # agent = {'id': 11, 'agent_id': '3c3d93a086a0cf95e2ef7bf9fbd0e473', 'agent_name': '审计知识库', 'agent_name_en': 'MI', 'access_token': 'd3195270-e7b7-4c80-802c-fde89d858740', 'env': 'prod', 'is_valid': 1}
    # agent = {'id': 5, 'agent_id': '007c529739690a861ad158e4237fea06', 'agent_name': '上汽大众客服知识库', 'access_token': '224ee926-559d-4435-b95a-098076e67353', 'env': 'prod'}
    # agent = {'id': 8, 'agent_id': 'f69b984010fd9c6d03e1e7387938af0e', 'agent_name': '研发知识库', 'access_token': '3dde0816-3828-4447-9dfd-4ccf4fe9a243', 'env': 'prod'}
    # agent = {'id': 8, 'agent_id': 'f69b984010fd9c6d03e1e7387938af0e', 'agent_name': '研发知识库', 'agent_name_en': 'E', 'access_token': '3dde0816-3828-4447-9dfd-4ccf4fe9a243', 'env': 'prod', 'is_valid': 1}
    # # agent = {'id': 1, 'agent_id': 'ba96968f61b6b72a6ef97c87ab99bee6', 'agent_name': '零号员工知识库', 'agent_name_en': 'EmpZero', 'access_token': '99f747e3-687b-4a5f-81c3-51513ea192a5', 'env': 'prod', 'is_valid': 1}
    # agent_id = agent['agent_id']
    # query_date = '2025-02-21 19:54:24'
    # results = cskb_operation.get_updated_cskb_results(agent=agent, query_date=query_date)
    # print(results)
    # agents = get_cskb_agents_info()
    # print(agents)
    # results = cskb_operation.get_agent_all_knowledge(agent)
    # results = [{'id': 'c1_c56a98c4b926dfc919ab9a736365d848', 'originalId': 'a1_95624089309d7b9d004425d4e05bf1d0'}]
    # print(results)
    # chunk_list = load_and_chunk_base_optimized(results)
    # print(chunk_list)

    # chunk_list = load_and_chunk_base(agent, results)
    # asyncio.run(optimized_upload_strategy(agent, chunk_list))
    # upload_chunk(agent, chunk_list)
    # results = cskb_operation.get_all_updated_cskb_results(agent=agent, query_date=query_date)
    # print(results)
    # chunk_list = load_and_chunk_base(agent, results)
    # upload_chunk(agent, chunk_list)
    # cskb_operation.upload_update_cskb_knowledge(agent, results)
    # print(results)

    