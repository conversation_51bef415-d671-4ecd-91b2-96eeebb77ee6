import pandas as pd
import os
import asyncio
from collections import defaultdict
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from langchain.docstore.document import Document
from langchain.text_splitter import RecursiveCharacterTextSplitter, SpacyTextSplitter, NLTKTextSplitter

from env import ENVIRONMENT
from config import cskb_to_milvus_dict
from rag.embedding import upload_knowledge, batch_upload_knowledge, async_batch_upload_knowledge

# 定义处理策略配置
CHUNK_STRATEGIES = {
    'faq_single': {
        'chunk_size': 0,
        'chunk_overlap': 0,
        'chunk_method': 'SingleChunk'
    },
    'single_chunk': {
        'chunk_size': 0,
        'chunk_overlap': 0,
        'chunk_method': 'SingleChunk'
    },
    'spacy_large': {
        'chunk_size': 30000,
        'chunk_overlap': 10000,
        'chunk_method': 'Spacy'
    },
    'spacy_small': {
        'chunk_size': 1000,
        'chunk_overlap': 100,
        'chunk_method': 'Spacy'
    }
}

def get_processing_strategy(item):
    """根据知识项特征确定处理策略"""
    if item['type'] == 'faq':
        return 'faq_single'
    
    doc = item['data']
    dir_name = doc['dir_name']
    
    # 特殊知识库使用单块策略
    if ("[项目知了知识库]" in dir_name or 
        "[智慧党建知识库]" in dir_name or
        (item['type'] == 'doc' and "[RiSE谈判助手知识库]" in dir_name)):
        return 'single_chunk'
    
    # RiSE谈判助手的attach使用大块策略
    if item['type'] == 'attach' and "[RiSE谈判助手知识库]" in dir_name:
        return 'spacy_large'
    
    # 其他情况使用小块策略
    return 'spacy_small'

def group_knowledge_by_strategy(knowledge_list):
    """按处理策略对知识项进行分组"""
    strategy_groups = defaultdict(list)
    
    for item in knowledge_list:
        strategy = get_processing_strategy(item)
        strategy_groups[strategy].append(item)
    
    return strategy_groups

def create_text_splitter(strategy_config):
    """根据策略配置创建文本分割器"""
    chunk_method = strategy_config['chunk_method']
    chunk_size = strategy_config['chunk_size']
    chunk_overlap = strategy_config['chunk_overlap']
    
    if chunk_method == "RCTS": 
        return RecursiveCharacterTextSplitter(
            chunk_size=chunk_size, 
            chunk_overlap=chunk_overlap, 
            length_function=len, 
            separators=['\n问题：', "\n## ", "\n# "]
        )
    elif chunk_method == "NLTK":
        return NLTKTextSplitter(
            chunk_size=chunk_size, 
            chunk_overlap=chunk_overlap, 
            length_function=len
        )
    elif chunk_method == "Spacy":
        return SpacyTextSplitter(
            chunk_size=chunk_size, 
            chunk_overlap=chunk_overlap, 
            length_function=len, 
            pipeline="zh_core_web_sm", 
            max_length=2500000
        )
    else:  # SingleChunk 或其他
        return None

def batch_process_chunks(items_group, strategy_config):
    """批量处理相同策略的知识项"""
    chunked_docs = []
    text_splitter = create_text_splitter(strategy_config)
    
    print(f"开始批量处理 {len(items_group)} 个知识项，策略: {strategy_config['chunk_method']}")
    
    for item in items_group:
        try:
            if item['type'] == 'faq':
                docs = process_faq_item(item, text_splitter, strategy_config)
            else:
                docs = process_doc_item(item, text_splitter, strategy_config)
            chunked_docs.extend(docs)
        except Exception as e:
            print(f"处理知识项失败: {e}, 项目: {item.get('data', {}).get('title', 'Unknown')}")
        
        # break
    
    return chunked_docs

def process_faq_item(item, text_splitter, strategy_config):
    """处理FAQ类型的知识项"""
    faq_dict = item['data']
    faq_question = faq_dict['question']
    
    # FAQ只有一个chunk，直接返回问题内容
    chunks = [faq_question]
    
    return create_documents_from_chunks(
        chunks=chunks,
        chunk_type="cskb_faq",
        item_data=faq_dict,
        chunk_content=faq_dict['answer']
    )

def process_doc_item(item, text_splitter, strategy_config):
    """处理文档类型的知识项"""
    doc_dict = item['data']
    doc_name = doc_dict['title']
    
    # 确定chunk类型
    chunk_type = "cskb_attach" if item['type'] == "attach" else "cskb_doc"
    
    # 组合内容
    if doc_name:
        new_chunk_content = f"知识文档标题：{doc_name}\n知识文档内容：\n{doc_dict['docText']}"
    else:
        new_chunk_content = doc_dict['docText']
    
    # 检查内容长度
    if len(new_chunk_content) > 1000000:
        print(f"文档内容过大，跳过分块: {doc_name}")
        return []
    
    # 分块处理
    if strategy_config['chunk_method'] == 'SingleChunk':
        chunks = [new_chunk_content]
    else:
        chunks = text_splitter.split_text(new_chunk_content)

    
    record_doc_chunk_info(chunks, doc_dict)
    
    return create_documents_from_chunks(
        chunks=chunks,
        chunk_type=chunk_type,
        item_data=doc_dict,
        chunk_content=doc_dict['docText']
    )


def record_doc_chunk_info(chunks, doc_dict):

# {'id': 'c1_df9c98540609f9082c37fbc20d0f525d', 'originalId': 'a1_b3d3dc7676fea947cc9ca497bc24927b', 'id_parent': '', 'title': '附件3：关键控制点变更流程图-更新版', 'agentId': '3c3d93a086a0cf95e2ef7bf9fbd0e473', 'agentName': '审计知识库', 'dirId': '51b0a66e4096e716210996cfcc01966b', 'originalId_parent': '', 'pubUserName': '梅瑞琪_66908', 'pubTime': '2025-05-14 17:56:57', 'version': 0.1, 'tags': [], 'attaches': [], 'wordNum': 0, 'pictureNum': None, 'linkNum': None, 'docText': '', 'docHtml': '<html>\n <head></head>\n <body>\n  <img src="/cskb-web/cskb/storage/v1/download?id=487776a8cac76c84dcad84765267ee89">\n </body>\n</html>', 'editType': 1, 'dir_name': '[审计知识库] 通用内部控制知识/程序文件及记录表样', 'dir_level': 2}

    today = pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')
    chunks_count = len(chunks)
    # chunk = chunks[-1]
    doc_category = doc_dict['dir_name']
    doc_id = doc_dict['id']
    doc_original_id = doc_dict['originalId']
    doc_name = doc_dict['title']
    doc_type = "文档"
    
    new_row = [doc_category, doc_type, doc_name, doc_original_id, doc_id, chunks_count, today]
    
    csv_path = "./data/chunks_info.csv"
    
    # 目录创建
    os.makedirs(os.path.dirname(csv_path), exist_ok=True)
    
    # 列定义
    columns = ["分类", "类型", "文档名", "原始ID", "文档ID", "分块数", "时间"]
    
    if os.path.exists(csv_path):
        # CSV 读取无需指定引擎
        df = pd.read_csv(csv_path, encoding='utf-8-sig')
        df = pd.concat([df, pd.DataFrame([new_row], columns=columns)], ignore_index=True)
    else:
        df = pd.DataFrame([new_row], columns=columns)
    
    # 文件写入
    df.to_csv(csv_path, index=False, encoding='utf-8-sig')
    

def create_documents_from_chunks(chunks, chunk_type, item_data, chunk_content):
    """从分块创建文档对象"""
    chunked_docs = []
    
    for i, chunk in enumerate(chunks):
        doc = Document(
            page_content=chunk.strip(),
            metadata={
                "type": chunk_type,
                "agent_id": item_data['agentId'],
                "agent_name": item_data['agentName'],
                "doc_id": item_data['id'],
                "doc_id_parent": item_data.get('id_parent', ''),
                "source_id": item_data['originalId'],
                "source_id_parent": item_data.get('originalId_parent', ''),
                "source": item_data.get('title', item_data.get('question', '')),
                "category": item_data['dir_name'],
                "dirId": item_data['dirId'],
                "chunk": i + 1,
                "faq_answer": chunk_content.strip() if chunk_type == "cskb_faq" else ""
            }
        )
        chunked_docs.append(doc)
    
    return chunked_docs

def load_and_chunk_base_optimized(knowledge_list: list):
    """优化版本：按策略聚合处理"""
    print("开始按处理策略分组知识项...")
    
    # 1. 按策略分组
    strategy_groups = group_knowledge_by_strategy(knowledge_list)
    
    print(f"分组完成，共 {len(strategy_groups)} 个处理组:")
    for strategy, items in strategy_groups.items():
        print(f"  - {strategy}: {len(items)} 个知识项")
    
    # 2. 并行处理各个策略组
    chunk_list = []
    completed_count = 0
    
    with ThreadPoolExecutor(max_workers=4) as executor:
        futures = {}
        
        for strategy, items in strategy_groups.items():
            strategy_config = CHUNK_STRATEGIES[strategy]
            future = executor.submit(batch_process_chunks, items, strategy_config)
            futures[future] = (strategy, len(items))
        
        for future in as_completed(futures):
            try:
                result = future.result()
                strategy, item_count = futures[future]
                chunk_list.extend(result)
                completed_count += item_count
                
                print(f"✓ 完成策略组 {strategy}: {item_count} 个知识项 -> {len(result)} 个分块")
                print(f"总进度: {completed_count}/{len(knowledge_list)} ({round(completed_count/len(knowledge_list)*100, 2)}%)")
                
            except Exception as e:
                strategy, item_count = futures[future]
                print(f"× 策略组处理失败 {strategy}: {e}")
                completed_count += item_count
    
    print(f"分块完成！总计: {len(chunk_list)} 个分块")
    return chunk_list


def upload_chunk(agent: dict, chunk_list: list):
    database = 'CSKB_' + agent['agent_name_en']
    BATCH_SIZE = 50  # 根据向量库接口承受能力调整
    for chunk_type in ['cskb_attach', 'cskb_doc', 'cskb_faq']:
        items = [{"text": chunk.page_content,"metadata": chunk.metadata} for chunk in chunk_list if chunk.metadata['type'] == chunk_type]
        collection = chunk_type.split('_')[1]
        if items:
            # 分批处理
            for i in range(0, len(items), BATCH_SIZE):
                batch = items[i:i+BATCH_SIZE]
                collection = chunk_type.split('_')[1]
                batch_upload_knowledge(items=batch, collection=collection, database=database, embedding_type='azure-openai')



async def optimized_upload_strategy(agent: dict, knowledge_list: list):
    """异步并行上传策略（支持失败重试）"""
    
    # 1. 全量分块（保持当前逻辑）
    chunk_list = load_and_chunk_base_optimized(knowledge_list)
    
    # 2. 并行上传参数配置
    DATABASE = 'CSKB_' + agent['agent_name_en']
    MAX_CONCURRENT_UPLOADS = 8  # 并行上传任务数
    BATCH_SIZE = 100  # 单次请求批次大小
    
    # 3. 按类型分组，减少索引切换
    from collections import defaultdict
    type_batches = defaultdict(list)
    
    for chunk in chunk_list:
        collection = chunk.metadata['type'].split('_')[1]
        type_batches[collection].append({
            "text": chunk.page_content,
            "metadata": chunk.metadata
        })
    

    async def _upload_batch(collection: str, items: list):
        """带重试机制的单个批次上传"""
        max_retries = 3
        for attempt in range(max_retries):
            try:
                result = await async_batch_upload_knowledge(
                    items=items,
                    collection=collection,
                    database=DATABASE,
                    batch_size=BATCH_SIZE
                )
                print(f"✓ 成功上传 {collection}: {len(items)} 条")
                return result
            except Exception as e:
                if attempt == max_retries - 1:
                    print(f"× 上传失败 {collection}: {str(e)}")
                    raise
                await asyncio.sleep(2 ** attempt)
    
    # 创建所有上传任务
    tasks = []
    for collection, items in type_batches.items():
        # 拆分超大请求为批次
        for i in range(0, len(items), BATCH_SIZE):
            batch = items[i:i+BATCH_SIZE]
            task = _upload_batch(collection, batch)
            tasks.append(task)
    
    # 使用信号量控制并发数
    semaphore = asyncio.Semaphore(MAX_CONCURRENT_UPLOADS)
    async with semaphore:
        results = await asyncio.gather(*tasks, return_exceptions=True)
    
    # 统计上传结果
    success_count = sum(1 for r in results if not isinstance(r, Exception))
    print(f"上传完成！成功批次: {success_count}/{len(tasks)}")